### **小学生教辅资料小程序 \- 功能与界面设计说明书 (增强版)**

版本：1.1  
目的：定义产品的功能范围、界面元素、交互逻辑、数据库结构与后台管理规范，作为UI设计与前后端开发的统一依据。

### **第一章：设计原则与整体导航**

#### **1.1 设计原则**

* **简洁直观**：界面元素清晰，避免不必要的装饰，让用户第一眼就知道如何操作。  
* **儿童友好**：色彩明快，图标形象，字体圆润，营造轻松、积极的学习氛围。  
* **目标导向**：核心操作（如查找资料、下载）的路径要短，按钮要醒目。

#### **1.2 小程序主导航 (Tab Bar)**

小程序底部采用固定的标签栏导航，包含以下三个核心入口：

* **\[首页\]**：默认选中。核心的资料发现和快速查找入口。  
* **\[分类\]**：提供更强大的、多维度组合筛选功能，满足有明确目标的用户。  
* **\[我的\]**：承载用户个人信息、积分管理和各项服务的入口。

### **第二章：关键页面功能详述**

#### **2.1 首页 (Home)**

**页面目标**：作为小程序的门户，提供最直接的资料查找入口和内容引导，吸引用户深入探索。

**界面元素设计与内容展示**

1. **顶部搜索框**  
   * **UI元素**：一个圆角矩形搜索框，占据屏幕主要宽度。  
   * **内容展示**：内部包含一个“放大镜”图标和灰色提示文字，如“搜索试卷、练习册...”。  
   * **用户操作**：点击后，跳转至独立的“搜索页面”。  
2. **年级分类导航**  
   * **UI元素**：一个 2x3 或 3x2 的宫格（Grid）布局。每个格子是一个视觉吸引力强的卡片。  
   * **内容展示**：每个卡片内包含生动的年级图标（如：一年级-小豆苗，六年级-博士帽）和清晰的“一年级”、“二年级”等文字。  
   * **用户操作**：点击任意年级卡片，跳转至“资料列表页”，并自动筛选出对应年级的全部资料。  
3. **推荐内容区 (可选)**  
   * **UI元素**：一个带有标题（如“最新上传”）的横向滚动列表。  
   * **内容展示**：以小卡片形式展示多份资料，每张卡片包含资料封面图、标题和所需积分。  
   * **用户操作**：可左右滑动浏览更多；点击任意卡片，跳转至对应的“资料详情页”。

#### **2.2 资料列表页 (Material List)**

**页面目标**：高效地展示符合用户筛选条件的资料，并引导用户找到心仪的目标。

**界面元素设计与内容展示**

1. **多维度筛选器**  
   * **UI元素**：固定在页面顶部的筛选栏，包含“科目”、“教材版本”、“学期”、“资料类型”等多个筛选按钮。  
   * **内容展示**：每个按钮默认显示分类名称，如“科目”。当用户选择后，按钮文字更新为所选项，如“数学”，并呈现高亮状态（例如，背景变蓝，文字变白）。  
   * **用户操作**：  
     * 点击任一筛选按钮（如“科目”），会从下方**弹出**一个包含所有选项（语文、数学、英语...）的浮层。  
     * 用户在浮层中点击一个选项后，浮层收起，列表根据新的筛选条件组合，自动刷新内容。  
     * 可选择“重置”按钮，清空所有筛选条件。  
2. **资料列表**  
   * **UI元素**：一个垂直滚动的长列表。  
   * **内容展示（单个列表项）**：  
     * **封面图**：位于左侧，若无则显示默认图标。  
     * **标题**：位于右侧上方，加粗显示，最多显示两行，超出部分用“...”省略。  
     * **标签**：标题下方，用多个不同颜色的小圆角标签展示核心分类，如人教版 期末卷 PDF，增强信息辨识度。  
     * **信息行**：位于最下方，用一行灰色小字整合展示“下载量: 123 | 浏览量: 560”。  
     * **积分**：在列表项的最右侧，用醒目的橙色字体展示“50积分”。  
   * **用户操作**：  
     * 点击列表中的任意一项，进入该资料的“资料详情页”。  
     * 向下滑动列表到底部时，自动触发“上拉加载”，载入下一页数据。  
     * 在列表顶部向下滑动，触发“下拉刷新”，重新获取最新数据。  
3. **空状态页面**  
   * **UI元素**：当没有符合条件的资料时，列表区域会显示一个可爱的插画（如一个空书包）和一行提示文字。  
   * **内容展示**：“该分类下暂无资料，看看别的吧\~”

#### **2.3 资料详情页 (Material Detail)**

**页面目标**：全面展示单份资料的吸引力，并提供清晰的下载转化路径。

**界面元素设计与内容展示**

1. **资料核心信息区**  
   * **内容展示**：  
     * **标题**：页面顶部，用大号加粗字体显示完整标题。  
     * **元数据**：标题下方，用一行灰色小字展示“上传于: 2025-08-01 | 格式: PDF | 大小: 2.5MB”。  
     * **预览图 (可选)**：以图片轮播（Swiper）的形式，展示资料的前1-2页截图。  
     * **详细描述**：一个独立的、带有灰色背景的区块，展示资料的详细介绍文字。  
2. **积分与下载操作区**  
   * **UI元素**：一个视觉上非常突出的区块。  
   * **内容展示**：  
     * 用大号橙色字体显示“所需积分：50”。  
     * 紧接着用一行小字显示“我的积分余额：850”，让用户形成直观对比。  
   * **用户操作**：见下方的“底部悬浮操作栏”。  
3. **底部悬浮操作栏**  
   * **UI元素**：一个固定在页面底部的操作栏，不随页面滚动。  
   * **内容展示**：  
     * **左侧**：一个“收藏”按钮（星形图标）和一个“分享”按钮（分享图标）。  
     * **右侧**：一个占据主要宽度的、颜色为**活力橙**的\*\*“立即下载”\*\*大按钮。  
   * **用户操作与交互**：  
     * 点击“收藏”，图标变为实心，并提示“收藏成功”。再次点击则取消收藏。  
     * 点击“分享”，拉起微信的分享面板。  
     * **若积分足够**：点击“立即下载”，按钮出现加载中（loading）状态，同时调用云函数。成功后提示“即将打开文件...”。  
     * **若积分不足**：按钮变为灰色不可用，按钮文字变为“积分不足”。点击后弹出提示框：“积分不足，快去‘我的’页面通过分享或看视频赚取积分吧！”，并提供“去赚积分”的跳转按钮。

#### **2.4 个人中心页 (My Profile)**

**页面目标**：聚合用户个人资产（积分）与常用服务，并提供核心运营入口（分享、客服）。

**界面元素设计与内容展示**

1. **顶部用户信息卡**  
   * **UI元素**：一个带有柔和背景图或颜色渐变的大圆角卡片。  
   * **内容展示**：  
     * 居中展示用户**圆形头像**和下方的**用户昵称**。  
     * 在昵称下方，用“我的积分”标签和**大号加粗数字**醒目地展示积分余额。  
   * **用户操作**：点击此区域，可以进入个人资料编辑页（如果未来有此功能）。  
2. **积分服务区**  
   * **UI元素**：一个横向排列的、包含两个入口的区域。  
   * **内容展示**：  
     * **左侧入口**：“积分明细”，点击后跳转到积分流水页面。  
     * **右侧入口**：“去赚积分”，用更醒目的设计（如带边框、橙色文字）突出，点击后弹出规则说明或直接跳转到可以看广告的页面。  
3. **功能列表**  
   * **UI元素**：标准列表样式，每行左侧是图标，中间是功能名称，右侧是向右的箭头。  
   * **内容展示**：  
     * “我的下载”：查看已下载过的资料历史。  
     * “我的收藏”：查看已收藏的资料列表。  
     * “分享小程序”：一个视觉上更突出的入口，如使用“邀请好友，同获积分奖励”的文案。点击后直接拉起分享面板。  
     * “联系客服”：点击后触发微信的在线客服功能。  
     * “帮助与反馈”：跳转到帮助说明或意见反馈页面。

### **第三章：用户功能详细说明 **

本章节以清单形式，详细定义各功能模块的需求点。

#### **3.1 核心流程**

* **新用户流程**：进入小程序 \-\> 弹出授权窗口 \-\> 同意授权 \-\> 自动登录并创建用户记录 \-\> 浏览资料。  
* **被邀请新用户流程**：通过分享链接进入 \-\> 弹出授权窗口 \-\> 同意授权 \-\> 自动登录并创建用户记录，同时为邀请者增加积分 \-\> 浏览资料。  
* **老用户流程**：进入小程序 \-\> 自动静默登录 \-\> 浏览资料。  
* **积分获取流程**：点击“赚积分”按钮 \-\> 完整观看激励视频 \-\> 积分到账提示 \-\> 返回页面。  
* **资料下载流程**：浏览并找到资料 \-\> 进入详情页 \-\> 点击下载 \-\> 判断积分是否足够 \-\> 扣除积分 \-\> 下载并打开文件。

#### **3.2 积分系统详细规则**

* **\[必须\] 积分获取规则**：
  * **新用户注册**：首次授权登录获得50积分作为新手礼包
  * **观看激励视频**：完整观看后获得10积分，每日最多观看5次（防刷限制）
  * **邀请新用户**：分享链接被新用户点击进入并授权后，邀请者获得30积分，被邀请者获得20积分
  * **每日签到（可选）**：连续签到1-7天分别获得5、5、10、10、15、15、20积分，第8天重新循环
* **\[必须\] 积分消耗规则**：
  * 下载资料时，扣除对应积分（由资料的`points_cost`字段决定）
  * 积分不足时无法下载，需要通过观看广告或邀请好友获取积分
* **\[必须\] 防刷机制**：
  * 观看广告：每日限制5次，每次观看间隔至少30秒
  * 邀请奖励：同一设备/IP在24小时内最多为同一分享者贡献1次奖励
  * 签到：必须间隔至少20小时才能进行下一次签到
* **\[必须\] 积分有效期**：
  * 积分永久有效，不设过期时间
  * 用户注销账号时积分清零
* **\[必须\] 规则透明**：应有单独的页面或弹窗，清晰地向用户说明所有积分的获取和使用规则。

### **第四章：数据库设计详述 (新增)**

本章节定义了项目在腾讯云开发（CloudBase）环境下的所有核心数据结构。

#### **4.1 users 集合 (用户信息表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| \_openid | String | 是 | 用户在小程序下的唯一标识 |
| nickName | String | 是 | 用户微信昵称 |
| avatarUrl | String | 是 | 用户微信头像的URL地址 |
| points | Number | 是 | 用户当前拥有的积分余额，默认为0 |
| createTime | Date | 是 | 用户记录的创建时间 |

#### **4.2 materials 集合 (教辅资料表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| title | String | 是 | 资料的完整标题 |
| description | String | 否 | 对资料的详细描述 |
| points\_cost | Number | 是 | 下载本资料需要消耗的积分数 |
| file\_id | String | 是 | 文件在云存储中的唯一ID (Cloud ID) |
| file\_size | Number | 是 | 文件大小，单位为字节 |
| file\_format | String | 是 | 文件格式，如PDF、DOCX等 |
| cover\_image | String | 否 | 封面图片的云存储ID，无则使用默认图标 |
| preview\_images | Array | 否 | 预览图片的云存储ID数组，用于详情页轮播 |
| category\_ids | Array | 是 | 关联到categories集合的\_id数组 |
| download\_count | Number | 是 | 下载次数，默认为0 |
| view\_count | Number | 是 | 浏览次数，默认为0 |
| upload\_time | Date | 是 | 资料上传时间 |
| is\_active | Boolean | 是 | 是否上架，true为上架 |
| sort\_order | Number | 否 | 排序权重，数字越小越靠前，用于推荐排序 |

#### **4.3 categories 集合 (动态分类表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID，建议使用有意义的字符串如"grade"、"subject" |
| name | String | 是 | 维度的中文名称，如"年级"、"科目" |
| sort\_order | Number | 否 | 排序权重，数字越小越靠前 |
| options | Array | 是 | 该维度下的所有选项，数组内是对象，格式见下方说明 |

**options数组中对象的结构：**
```json
{
  "id": "grade_1",           // 选项的唯一标识
  "name": "一年级",          // 选项的显示名称
  "sort_order": 1,          // 选项排序权重
  "is_active": true         // 是否启用该选项
}
```

#### **4.4 config 集合 (全局配置表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 固定为main |
| new\_user\_points | Number | 是 | 新用户注册奖励积分数，默认50 |
| enable\_ad\_reward | Boolean | 是 | 是否开启“观看广告赚积分”功能 |
| ad\_reward\_points | Number | 是 | 观看一次广告奖励的积分数，默认10 |
| ad\_daily\_limit | Number | 是 | 每日观看广告次数限制，默认5 |
| ad\_interval\_seconds | Number | 是 | 观看广告间隔时间（秒），默认30 |
| enable\_share\_reward | Boolean | 是 | 是否开启“分享给新用户赚积分”功能 |
| share\_reward\_points | Number | 是 | 成功邀请新用户奖励的积分数，默认30 |
| invited\_user\_points | Number | 是 | 被邀请新用户获得的积分数，默认20 |
| enable\_checkin | Boolean | 是 | 是否开启签到功能 |
| checkin\_rewards | Array | 是 | 连续签到奖励数组，如[5,5,10,10,15,15,20] |

#### **4.5 points\_log 集合 (积分流水表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| user\_openid | String | 是 | 关联到users集合的\_openid |
| amount | Number | 是 | 变动金额，正数为增加，负数为减少 |
| balance | Number | 是 | 本次变动后用户的积分余额 |
| type | String | 是 | 变动类型，如ad\_reward, download\_cost |
| description | String | 是 | 对本次变动的详细描述 |
| createTime | Date | 是 | 流水记录的创建时间 |

#### **4.6 user_favorites 集合 (用户收藏表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| user\_openid | String | 是 | 关联到users集合的\_openid |
| material\_id | String | 是 | 关联到materials集合的\_id |
| createTime | Date | 是 | 收藏时间 |

#### **4.7 user_downloads 集合 (用户下载历史表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| user\_openid | String | 是 | 关联到users集合的\_openid |
| material\_id | String | 是 | 关联到materials集合的\_id |
| points\_cost | Number | 是 | 本次下载消耗的积分数（记录历史价格） |
| download\_time | Date | 是 | 下载时间 |

#### **4.8 share_records 集合 (分享记录表)**

| 字段名 | 数据类型 | 是否必填 | 描述 |
| :---- | :---- | :---- | :---- |
| \_id | String | 是 | 记录的唯一ID |
| sharer\_openid | String | 是 | 分享者的openid |
| share\_code | String | 是 | 分享码，用于追踪分享来源 |
| new\_user\_openid | String | 否 | 通过此分享进入的新用户openid |
| reward\_given | Boolean | 是 | 是否已发放奖励，默认false |
| share\_time | Date | 是 | 分享时间 |
| join\_time | Date | 否 | 新用户加入时间 |

### **第五章：技术实现方案详述 (新增)**

本章节定义核心功能的具体技术实现方案，确保开发过程中的技术选型和实现路径清晰。

#### **5.1 文件预览与下载**

* **\[文件预览实现\]**：
  * **PDF文件**：使用微信小程序的`wx.openDocument` API，支持在线预览PDF文件
  * **图片预览**：在详情页使用`preview_images`字段展示资料前1-2页的截图，使用`wx.previewImage`实现图片预览
  * **其他格式**：DOCX、PPT等格式通过云函数转换为PDF后预览，或提供格式说明引导用户下载
* **\[下载实现\]**：
  * 使用云存储的临时链接（`wx.cloud.getTempFileURL`）获取下载地址
  * 调用`wx.downloadFile`下载文件到本地临时目录
  * 下载完成后自动调用`wx.openDocument`打开文件
  * 下载过程中显示进度条，支持取消下载

#### **5.2 搜索功能实现**

* **\[搜索策略\]**：
  * **数据库搜索**：使用云数据库的正则表达式查询，对`title`和`description`字段进行模糊匹配
  * **搜索索引**：为提高性能，在`materials`表的`title`字段创建文本索引
  * **搜索历史**：在本地存储用户的搜索历史，最多保存10条
* **\[搜索优化\]**：
  * 实现搜索防抖，用户停止输入500ms后才发起搜索请求
  * 支持按热度排序（下载量+浏览量）和时间排序
  * 搜索结果高亮显示匹配的关键词

#### **5.3 分享功能实现**

* **\[分享机制\]**：
  * 生成唯一的分享码（`share_code`），记录在`share_records`表中
  * 分享链接格式：小程序码 + 分享码参数
  * 新用户通过分享链接进入时，解析分享码并记录来源
* **\[奖励发放\]**：
  * 新用户完成授权登录后，检查是否通过分享链接进入
  * 验证分享码有效性，为分享者发放积分奖励
  * 更新`share_records`表的`reward_given`状态，防止重复奖励

#### **5.4 积分系统实现**

* **\[激励视频广告\]**：
  * 使用微信小程序激励视频广告组件
  * 监听广告播放完成事件，确保用户完整观看
  * 广告播放完成后调用云函数发放积分奖励
* **\[积分操作\]**：
  * 所有积分变动通过云函数处理，确保数据一致性
  * 每次积分变动都记录到`points_log`表，便于审计和查询
  * 使用数据库事务确保积分扣除和资料下载的原子性

### **第六章：异常处理与用户体验设计 (新增)**

本章节定义各种异常情况的处理方案和用户体验优化措施，确保小程序在各种网络环境下都能提供良好的用户体验。

#### **6.1 加载状态设计**

* **\[页面加载\]**：
  * **首次进入**：显示骨架屏（Skeleton Screen），模拟页面结构，避免白屏
  * **列表加载**：显示加载动画，使用微信小程序的`wx.showLoading`
  * **上拉加载更多**：列表底部显示"加载中..."文字和转圈动画
  * **下拉刷新**：使用小程序原生的下拉刷新组件
* **\[文件下载\]**：
  * 显示下载进度条，实时更新下载百分比
  * 提供"取消下载"按钮，允许用户中断下载
  * 下载完成后显示"下载成功"提示，自动打开文件

#### **6.2 错误处理机制**

* **\[网络错误\]**：
  * **无网络连接**：显示"网络连接失败，请检查网络设置"，提供"重试"按钮
  * **请求超时**：显示"请求超时，请稍后重试"，自动重试3次
  * **服务器错误**：显示"服务暂时不可用，请稍后重试"
* **\[业务错误\]**：
  * **积分不足**：显示明确的提示信息和获取积分的引导按钮
  * **文件不存在**：显示"文件已失效或被删除"，引导用户浏览其他资料
  * **权限不足**：显示"需要授权才能使用此功能"，引导用户重新授权
* **\[用户操作错误\]**：
  * **搜索无结果**：显示友好的空状态页面，建议用户尝试其他关键词
  * **重复操作**：如重复收藏、重复下载，给出相应提示

#### **6.3 网络异常降级方案**

* **\[弱网环境优化\]**：
  * 图片懒加载，只加载可视区域内的图片
  * 启用图片压缩，根据网络状况调整图片质量
  * 实现请求缓存，避免重复请求相同数据
* **\[离线功能\]**：
  * 缓存用户的收藏列表和下载历史
  * 缓存常用的分类数据，减少网络请求
  * 已下载的文件支持离线查看
* **\[数据同步\]**：
  * 网络恢复后自动同步用户操作（如收藏、浏览记录）
  * 使用乐观更新策略，先更新UI再同步服务器

#### **6.4 设备适配与兼容性**

* **\[屏幕适配\]**：
  * 支持不同尺寸的手机屏幕，使用响应式设计
  * 适配iPhone X系列的刘海屏和底部安全区域
  * 考虑横屏使用场景（主要是文件预览时）
* **\[性能优化\]**：
  * 长列表使用虚拟滚动，避免内存溢出
  * 图片使用WebP格式，减少加载时间
  * 合理使用小程序的分包加载功能
* **\[兼容性处理\]**：
  * 兼容微信版本差异，对不支持的API提供降级方案
  * 处理不同Android系统的文件打开方式差异

### **第七章：管理后台设计**

本章节定义了管理员如何通过**腾讯云开发内容管理系统 (CMS)** 对小程序进行日常维护和运营。

#### **5.1 资料管理**

* **\[必须\] 增删改查**：管理员可以上传、编辑和删除教辅资料。  
* **\[必须\] 文件上传**：支持直接上传PDF、DOCX等格式的文件到云存储。  
* **\[必须\] 字段编辑**：可编辑资料的标题、描述、所需积分、封面图等所有信息。  
* **\[必须\] 分类关联**：在编辑资料时，可以方便地为其关联多个分类标签（如勾选“三年级”、“数学”、“人教版”）。

#### **5.2 分类管理**

* **\[必须\] 维度管理**：可以增删改“年级”、“科目”、“教材版本”等分类维度。  
* **\[必须\] 选项管理**：可以为每个维度增删改具体的选项（如为“年级”维度添加“学前班”选项）。  
* **\[必须\] 排序功能**：支持对维度和选项进行拖拽排序，决定其在小程序前端的显示顺序。

#### **5.3 用户管理**

* **\[必须\] 用户列表**：可查看所有用户的列表，包括其OpenID、昵称、头像和当前积分。  
* **\[必须\] 用户搜索**：支持通过昵称或OpenID搜索特定用户。  
* **\[可选\] 积分修改**：为特殊运营活动，管理员可以手动为某个用户增加或减少积分，并记录操作原因。

#### **5.4 系统配置**

* **\[必须\] 积分策略配置**：  
  * 提供开关，用于开启/关闭“观看广告赚积分”功能。  
  * 提供输入框，用于设定观看广告奖励的积分数。  
  * 提供开关，用于开启/关闭“分享赚积分”功能。  
  * 提供输入框，用于设定成功邀请新用户奖励的积分数。

### **第八章：数据示例与配置说明 (新增)**

本章节提供各数据表的示例数据和详细的配置说明，帮助开发者更好地理解数据结构和业务逻辑。

#### **8.1 数据表示例**

**categories 集合示例数据：**
```json
[
  {
    "_id": "grade",
    "name": "年级",
    "sort_order": 1,
    "options": [
      {"id": "grade_1", "name": "一年级", "sort_order": 1, "is_active": true},
      {"id": "grade_2", "name": "二年级", "sort_order": 2, "is_active": true},
      {"id": "grade_3", "name": "三年级", "sort_order": 3, "is_active": true},
      {"id": "grade_4", "name": "四年级", "sort_order": 4, "is_active": true},
      {"id": "grade_5", "name": "五年级", "sort_order": 5, "is_active": true},
      {"id": "grade_6", "name": "六年级", "sort_order": 6, "is_active": true}
    ]
  },
  {
    "_id": "subject",
    "name": "科目",
    "sort_order": 2,
    "options": [
      {"id": "chinese", "name": "语文", "sort_order": 1, "is_active": true},
      {"id": "math", "name": "数学", "sort_order": 2, "is_active": true},
      {"id": "english", "name": "英语", "sort_order": 3, "is_active": true}
    ]
  },
  {
    "_id": "textbook",
    "name": "教材版本",
    "sort_order": 3,
    "options": [
      {"id": "renjiao", "name": "人教版", "sort_order": 1, "is_active": true},
      {"id": "beijing", "name": "北师大版", "sort_order": 2, "is_active": true},
      {"id": "sujiao", "name": "苏教版", "sort_order": 3, "is_active": true}
    ]
  },
  {
    "_id": "material_type",
    "name": "资料类型",
    "sort_order": 4,
    "options": [
      {"id": "test_paper", "name": "试卷", "sort_order": 1, "is_active": true},
      {"id": "exercise", "name": "练习册", "sort_order": 2, "is_active": true},
      {"id": "courseware", "name": "课件", "sort_order": 3, "is_active": true}
    ]
  }
]
```

**materials 集合示例数据：**
```json
{
  "_id": "material_001",
  "title": "三年级数学上册期末测试卷（人教版）",
  "description": "本试卷涵盖三年级数学上册全部知识点，包括加减法、乘除法、图形认识等内容，适合期末复习使用。",
  "points_cost": 20,
  "file_id": "cloud://env-xxx.xxx/materials/math_grade3_final.pdf",
  "file_size": 2048576,
  "file_format": "PDF",
  "cover_image": "cloud://env-xxx.xxx/covers/math_grade3_cover.jpg",
  "preview_images": [
    "cloud://env-xxx.xxx/previews/math_grade3_p1.jpg",
    "cloud://env-xxx.xxx/previews/math_grade3_p2.jpg"
  ],
  "category_ids": ["grade_3", "math", "renjiao", "test_paper"],
  "download_count": 156,
  "view_count": 423,
  "upload_time": "2025-08-01T10:30:00.000Z",
  "is_active": true,
  "sort_order": 100
}
```

**config 集合示例数据：**
```json
{
  "_id": "main",
  "new_user_points": 50,
  "enable_ad_reward": true,
  "ad_reward_points": 10,
  "ad_daily_limit": 5,
  "ad_interval_seconds": 30,
  "enable_share_reward": true,
  "share_reward_points": 30,
  "invited_user_points": 20,
  "enable_checkin": true,
  "checkin_rewards": [5, 5, 10, 10, 15, 15, 20]
}
```

#### **8.2 配置项详细说明**

* **new_user_points**: 新用户注册时获得的初始积分，建议设置为50-100，确保用户能下载几份免费资料体验
* **ad_reward_points**: 观看广告奖励积分，建议设置为10-20，平衡用户体验和变现效果
* **ad_daily_limit**: 每日观看广告限制，建议设置为3-10次，防止用户过度依赖广告获取积分
* **ad_interval_seconds**: 观看广告间隔，建议设置为30-60秒，防止恶意刷广告
* **share_reward_points**: 邀请奖励积分，建议设置为20-50，激励用户分享
* **checkin_rewards**: 签到奖励数组，建议设置递增模式，鼓励连续签到

#### **8.3 业务规则补充说明**

* **积分定价策略**: 建议普通资料10-30积分，精品资料30-100积分，确保用户通过1-2次广告或分享就能获得基础资料
* **分类扩展**: categories表支持动态扩展，可根据实际需求添加"学期"、"难度等级"等新维度
* **文件管理**: 建议在云存储中按年级/科目建立文件夹结构，便于管理和CDN缓存
* **数据备份**: 重要数据表建议设置定期备份，特别是用户积分和下载记录